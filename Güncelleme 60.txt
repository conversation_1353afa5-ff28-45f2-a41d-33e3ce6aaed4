<PERSON><PERSON><PERSON><PERSON>iklikler:
assets/css/dark-mode-override.css dosyasına eklenen dark mode stilleri:

Toast Notification Genel Stilleri:
Arka plan rengi: #2a2a2a (koyu gri)
Metin rengi: #ffffff (beyaz)
Kenarlık: #3a3a3a (daha açık gri)
Gölge efekti: 0 4px 12px rgba(0, 0, 0, 0.3) (koyu gölge)
Başarı Mesajları (Success):
Arka plan: #1e3a1e (koyu yeşil)
Metin: #a8e6a3 (açık yeşil)
Kenarlık: #2d5a2d (orta yeşil)
Hata Mesajları (Error):
Arka plan: #3a1e1e (koyu kırmızı)
Metin: #ffb3b3 (açık kırmızı)
Kenarlık: #5a2d2d (orta kırmızı)
Uyarı Mesajları (Warning):
Arka plan: #3a3a1e (koyu sarı)
Metin: #ffe6a3 (açık sarı)
Kenarlık: #5a5a2d (orta sarı)
Bilgi <PERSON> (Info):
Arka plan: #1e2a3a (koyu mavi)
Metin: #a3c7e6 (açık mavi)
Kenarlık: #2d3d5a (orta mavi)
Kapsanan Notification Sistemleri:
Tutor LMS toast notification sistemi
Genel alert sistemleri
Dashboard sayfalarına özel notification'lar
Para çekme sayfasına özel notification'lar
SweetAlert2 (Swal2) toast notification'ları
jQuery toast notification'ları
Genel popup notification'lar
Hedeflenen CSS Sınıfları:
.tutor-toast, .tutor-notification
.tutor-alert, .tutor-alert-success, .tutor-alert-error
.toast-container, .notification-container
.success-message, .error-message, .warning-message, .info-message
.swal2-toast, .jq-toast-wrap
Bu değişiklikler sayesinde para çekme işlemi sonrasında görünen "Request Successful" bildirim penceresi ve diğer tüm notification/toast mesajları dark mode'da uygun renklerde görünecektir. Arka plan koyu tema ile uyumlu, metin renkleri okunabilir ve kenarlık/gölge efektleri dark tema ile uyumlu hale getirilmiştir.