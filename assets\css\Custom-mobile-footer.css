/**
 * Custom Mobile Footer CSS
 * Bu dosya, mobil dashboard footer'daki elementlerin sırasını değiştirmek için kullanılır.
 *
 * @package DmrLMS
 * @since 1.0.1
 */

/* Mobil footer butonları için özel ikonlar - Sidebar ile aynı ikonlar kullanılıyor */
/* tutor-icon-book-open, tutor-icon-quiz ve tutor-icon-question ikonları Tutor LMS'in standart ikonlarıdır */
/* Bu ikonlar sidebar'daki menü öğeleriyle aynıdır */

/* 4 buton için özel düzenlemeler */
#tutor-dashboard-footer-mobile .tutor-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#tutor-dashboard-footer-mobile a {
    flex: 1;
    max-width: 25%;
}

/* Mobil footer genel stilleri */
#tutor-dashboard-footer-mobile {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Mobil footer içindeki butonlar */
#tutor-dashboard-footer-mobile a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    color: #212327;
    text-decoration: none;
    transition: color 0.3s ease;
}

/* Mobil footer içindeki ikonlar */
#tutor-dashboard-footer-mobile a i {
    font-size: 20px;
    margin-bottom: 5px;
}

/* Mobil footer içindeki metin */
#tutor-dashboard-footer-mobile a span {
    font-size: 12px;
}

/* Aktif buton stilleri */
#tutor-dashboard-footer-mobile a.active {
    color: var(--tutor-color-primary);
}

/* Dark Mode - Mobil Footer */
body.tutor-dark-mode #tutor-dashboard-footer-mobile,
html[data-theme="dark"] #tutor-dashboard-footer-mobile {
    background-color: #121212 !important;
    border-top: 1px solid #2A2A2A !important;
}

/* Dark Mode - Mobil Footer Butonları */
body.tutor-dark-mode #tutor-dashboard-footer-mobile a,
html[data-theme="dark"] #tutor-dashboard-footer-mobile a {
    color: #f5f5f5 !important;
}

/* Dark Mode - Mobil Footer Aktif Buton - Diğer butonlar gibi normal renkte */
body.tutor-dark-mode #tutor-dashboard-footer-mobile a.active,
html[data-theme="dark"] #tutor-dashboard-footer-mobile a.active {
    color: #f5f5f5 !important;
}

/* Mobil ve tablet cihazlarda buton tıklama animasyonu */
#tutor-dashboard-footer-mobile a:active {
    transform: scale(0.9);
    transition: transform 0.2s ease;
}

/* Tutor dashboard footer mobile için padding ayarı */
.tutor-dashboard #tutor-dashboard-footer-mobile {
    padding: 10px 0px 0px 0px;
}

/* Orijinal Tutor LMS medya sorgusunu geçersiz kıl */
@media (min-width: 991px) {
    .tutor-dashboard #tutor-dashboard-footer-mobile {
        display: none !important;
    }
}

/* Orijinal Tutor LMS medya sorgusunu engelle */
@media (min-width: 768px) and (max-width: 991px) {
    .tutor-dashboard #tutor-dashboard-footer-mobile {
        display: block !important;
    }
}

/* Mobil footer buton metinleri için responsive düzenlemeler */
@media (max-width: 480px) {
    #tutor-dashboard-footer-mobile a span {
        font-size: 10px;
        line-height: 1.2;
    }

    #tutor-dashboard-footer-mobile a {
        padding: 6px 2px;
    }

    #tutor-dashboard-footer-mobile a i {
        font-size: 16px;
        margin-bottom: 2px;
    }
}

/* Çok küçük ekranlar için metin kısaltma - 4 buton için optimize edildi */
@media (max-width: 360px) {
    #tutor-dashboard-footer-mobile a span {
        font-size: 9px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 60px;
    }

    #tutor-dashboard-footer-mobile a i {
        font-size: 14px;
        margin-bottom: 1px;
    }

    #tutor-dashboard-footer-mobile a {
        padding: 5px 1px;
    }
}
